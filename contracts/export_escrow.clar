(define-map escrows
  {
    escrow-id: uint
  }
  {
    owner: principal        ;; business owner initiating export
    agent: principal        ;; logistics or escrow agent
    customer: principal     ;; buyer or importer
    amount: uint            ;; amount locked in escrow (in micro-STX)
    status: (string-ascii 32) ;; "Open", "Shipped", "Completed", "Disputed", "Cancelled"
    deposit-timestamp: uint
  }
  )

(define-data-var next-escrow-id uint 1)

;; Constants for incentive amounts (in micro-STX)
(define-constant INCENTIVE_OWNER u1000000)   ;; 1 STX
(define-constant INCENTIVE_AGENT u500000)    ;; 0.5 STX
(define-constant INCENTIVE_CUSTOMER u250000) ;; 0.25 STX

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;; Escrow Lifecycle
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

(define-private (generate-escrow-id)
  (let ((id (var-get next-escrow-id)))
    (var-set next-escrow-id (+ id u1))
    id))

(define-public (create-escrow (agent principal) (customer principal) (amount uint))
  (begin
    ;; Caller must send correct STX amount with call
    (asserts! (is-eq (contract-caller) tx-sender) (err "Invalid caller"))
    (asserts! (>= amount u1) (err "Amount must be positive"))
    (let ((escrow-id (generate-escrow-id)))
      ;; Lock STX into contract
      (match (stx-transfer? amount tx-sender (as-contract tx-sender))
        success
        (begin
          (map-set escrows
            { escrow-id: escrow-id }
            { owner: tx-sender
              agent: agent
              customer: customer
              amount: amount
              status: "Open"
              deposit-timestamp: (get-block-info? timestamp) }
          )
          (ok escrow-id)
        )
        failure (err failure)
      )
    )
  )
)

(define-public (confirm-shipment (escrow-id uint))
  (let ((e (map-get escrows { escrow-id: escrow-id })))
    (match e
      escrow
      (begin
        (asserts! (is-eq tx-sender escrow.agent) (err "Only agent can confirm shipment"))
        (asserts! (is-eq escrow.status "Open") (err "Escrow not open"))
        (map-set escrows { escrow-id: escrow-id }
          { owner: escrow.owner
            agent: escrow.agent
            customer: escrow.customer
            amount: escrow.amount
            status: "Shipped"
            deposit-timestamp: escrow.deposit-timestamp })
        (ok true)
      )
      none (err "Escrow not found")
    )
  )
)

(define-public (release-funds (escrow-id uint))
  (let ((e (map-get escrows { escrow-id: escrow-id })))
    (match e
      escrow
      (begin
        (asserts! (is-eq tx-sender escrow.customer) (err "Only customer can release funds"))
        (asserts! (or (is-eq escrow.status "Open") (is-eq escrow.status "Shipped")) (err "Cannot release in current state"))
        ;; Transfer principal amount to owner
        (match (stx-transfer? escrow.amount tx-sender escrow.owner)
          success
          (begin
            ;; Distribute incentives
            (stx-transfer? INCENTIVE_OWNER (as-contract tx-sender) escrow.owner)
            (stx-transfer? INCENTIVE_AGENT (as-contract tx-sender) escrow.agent)
            (stx-transfer? INCENTIVE_CUSTOMER (as-contract tx-sender) escrow.customer)
            ;; Update status
            (map-set escrows { escrow-id: escrow-id }
              { owner: escrow.owner
                agent: escrow.agent
                customer: escrow.customer
                amount: escrow.amount
                status: "Completed"
                deposit-timestamp: escrow.deposit-timestamp })
            (ok true)
          )
          failure (err failure)
        )
      )
      none (err "Escrow not found")
    )
  )
)

(define-public (raise-dispute (escrow-id uint) (reason (string-ascii 128)))
  (let ((e (map-get escrows { escrow-id: escrow-id })))
    (match e
      escrow
      (begin
        ;; Only owner or customer can raise dispute
        (asserts! (or (is-eq tx-sender escrow.customer) (is-eq tx-sender escrow.owner)) (err "Not authorized"))
        (asserts! (not (is-eq escrow.status "Completed")) (err "Escrow already completed"))
        (map-set escrows { escrow-id: escrow-id }
          { owner: escrow.owner
            agent: escrow.agent
            customer: escrow.customer
            amount: escrow.amount
            status: "Disputed"
            deposit-timestamp: escrow.deposit-timestamp })
        (ok true)
      )
      none (err "Escrow not found")
    )
  )
)

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;; Admin Function: resolve dispute
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
(define-constant ERR_UNAUTHORIZED u100)
(define-constant ERR_NOT_DISPUTED u101)

(define-public (resolve-dispute (escrow-id uint) (release-to-owner bool))
  (begin
    ;; Only contract deployer can resolve
    (asserts! (is-eq tx-sender (contract-owner)) (err ERR_UNAUTHORIZED))
    (let ((e (map-get escrows { escrow-id: escrow-id })))
      (match e
        escrow
        (begin
          (asserts! (is-eq escrow.status "Disputed") (err ERR_NOT_DISPUTED))
          (begin
            ;; Decide payout
            (if release-to-owner
              (stx-transfer? escrow.amount (as-contract tx-sender) escrow.owner)
              (stx-transfer? escrow.amount (as-contract tx-sender) escrow.customer))
            (map-set escrows { escrow-id: escrow-id }
              { owner: escrow.owner
                agent: escrow.agent
                customer: escrow.customer
                amount: escrow.amount
                status: (if release-to-owner "Completed" "Cancelled")
                deposit-timestamp: escrow.deposit-timestamp })
            (ok true)
          )
        )
        none (err "Escrow not found")
      )
    )
  )
)

;; Read-only getters
(define-public (get-escrow (escrow-id uint))
  (match (map-get escrows { escrow-id: escrow-id })
    e (ok e)
    none (err "Not found")))
